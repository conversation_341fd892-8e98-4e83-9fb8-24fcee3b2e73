<script lang="ts">
	import { goto } from '$app/navigation';
	import { superForm } from 'sveltekit-superforms';
	import * as Form from '$lib/components/ui/form';
	import { Button, buttonVariants } from '$lib/components/ui/button';
	import { Input } from '$lib/components/ui/input';
	import { Textarea } from '$lib/components/ui/textarea';
	import * as Select from '$lib/components/ui/select';
	import * as Command from '$lib/components/ui/command';
	import * as Popover from '$lib/components/ui/popover';
	import {
		Dialog,
		DialogContent,
		DialogDescription,
		DialogFooter,
		DialogHeader,
		DialogTitle,
	} from '$lib/components/ui/dialog';
	import {
		Table,
		TableBody,
		TableCell,
		TableHead,
		TableHeader,
		TableRow,
	} from '$lib/components/ui/table';
	import {
		Card,
		CardContent,
		CardDescription,
		CardHeader,
		CardTitle,
	} from '$lib/components/ui/card';
	import { Badge } from '$lib/components/ui/badge';
	import * as DropdownMenu from '$lib/components/ui/dropdown-menu';
	import { toast } from 'svelte-sonner';
	import { riskStatuses } from '$lib/schemas/risk';
	import { formatCurrency } from '$lib/utils';
	import { cn } from '$lib/utils';
	import {
		Calendar as CalendarIcon,
		Plus as PlusIcon,
		Trash as TrashIcon,
		Pencil as PencilIcon,
		DotsThreeVertical as DotsThreeVerticalIcon,
		SpinnerGap as SpinnerGapIcon,
		CaretUpDown as CaretUpDownIcon,
		Check as CheckIcon,
		CaretUpDown as CaretUpDownIcon,
		Check as CheckIcon,
	} from 'phosphor-svelte';


	import { Calendar } from '$lib/components/ui/calendar';
	import { format } from 'date-fns';
	import {
		DateFormatter,
		type DateValue,
		getLocalTimeZone,
		parseDate,
		today,
	} from '@internationalized/date';
	import { SvelteURLSearchParams } from 'svelte/reactivity';
	import { tick } from 'svelte';
	import { useId } from 'bits-ui';
	import { tick } from 'svelte';
	import { useId } from 'bits-ui';

	const { data } = $props();

	// Extract data from props
	const { project, client, canEditProject, projectMembers } = data;
	const risks = $derived(data.risks);
	const rawWbsItems = $derived(data.wbsItems);

	// Transform wbsItems for combobox usage
	const wbsItems = $derived(
		rawWbsItems.map((item) => ({
			label: `${item.code} - ${item.description}`,
			value: item.wbs_library_item_id.toString(),
		})),
	);

	// Helper functions for combobox
	let filterOpen = $state(false);
	let filterTriggerRef = $state<HTMLButtonElement>(null!);
	let dialogOpen = $state(false);
	let dialogTriggerRef = $state<HTMLButtonElement>(null!);

	// We want to refocus the trigger button when the user selects
	// an item from the list so users can continue navigating the
	// rest of the form with the keyboard.
	function closeAndFocusTrigger(triggerId: string, isDialog: boolean = false) {
		if (isDialog) {
			dialogOpen = false;
		} else {
			filterOpen = false;
		}
		tick().then(() => {
			document.getElementById(triggerId)?.focus();
		});
	}
	const filterTriggerId = useId();
	const dialogTriggerId = useId();

	function getWbsItemName(wbsId: string | null | undefined): string {
		if (!wbsId) return 'None';

		const item = wbsItems.find((item) => item.value === wbsId);
		return item ? item.label : 'Unknown';
	}
	const rawWbsItems = $derived(data.wbsItems);

	// Transform wbsItems for combobox usage
	const wbsItems = $derived(
		rawWbsItems.map((item) => ({
			label: `${item.code} - ${item.description}`,
			value: item.wbs_library_item_id.toString(),
		})),
	);

	// Helper functions for combobox
	let filterOpen = $state(false);
	let filterTriggerRef = $state<HTMLButtonElement>(null!);
	let dialogOpen = $state(false);
	let dialogTriggerRef = $state<HTMLButtonElement>(null!);

	// We want to refocus the trigger button when the user selects
	// an item from the list so users can continue navigating the
	// rest of the form with the keyboard.
	function closeAndFocusTrigger(triggerId: string, isDialog: boolean = false) {
		if (isDialog) {
			dialogOpen = false;
		} else {
			filterOpen = false;
		}
		tick().then(() => {
			document.getElementById(triggerId)?.focus();
		});
	}
	const filterTriggerId = useId();
	const dialogTriggerId = useId();

	function getWbsItemName(wbsId: string | null | undefined): string {
		if (!wbsId) return 'None';

		const item = wbsItems.find((item) => item.value === wbsId);
		return item ? item.label : 'Unknown';
	}

	// Setup risk form
	const riskForm = superForm(data.form, {
		onUpdate({ form }) {
			if (form.message) {
				if (form.message.type === 'success') {
					toast.success(form.message.text);
					showRiskDialog = false;
				} else if (form.message.type === 'error') {
					toast.error(form.message.text);
				}
			}
		},
	});

	// Setup filter form
	const filterForm = superForm(data.filterForm, {
		onUpdated({ form }) {
			// Build URL with filter parameters
			const params = new SvelteURLSearchParams();

			if (form.data.status && form.data.status !== 'all') {
				params.set('status', form.data.status);
			}

			if (form.data.date_from) {
				params.set('date_from', form.data.date_from);
			}

			if (form.data.date_to) {
				params.set('date_to', form.data.date_to);
			}

			if (form.data.wbs_library_item_id) {
				params.set('wbs_item', form.data.wbs_library_item_id.toString());
			}

			// Navigate to the filtered URL
			goto(`?${params.toString()}`);
		},
	});

	// Form bindings
	const {
		enhance: enhanceRiskForm,
		form: riskFormData,
		delayed: delayedRiskForm,
		submitting: submittingRiskForm,
	} = riskForm;
	const {
		form: filterData,
		enhance: enhanceFilterForm,
		delayed: delayedFilterForm,
		submitting: submittingFilterForm,
	} = filterForm;

	// State for risk dialog
	let showRiskDialog = $state(false);
	let editingRisk = $state(false);

	// State for delete confirmation dialog
	let showDeleteDialog = $state(false);
	let riskToDelete = $state<(typeof data.risks)[0] | null>(null);

	// Function to open the risk form dialog for creating a new risk
	function addNewRisk() {
		editingRisk = false;
		$riskFormData.project_id = project.project_id;
		$riskFormData.risk_id = undefined;
		$riskFormData.title = '';
		$riskFormData.description = '';
		$riskFormData.status = 'identified';
		$riskFormData.wbs_library_item_id = null;
		$riskFormData.date_identified = today(getLocalTimeZone()).toString();
		$riskFormData.cause = null;
		$riskFormData.effect = null;
		$riskFormData.program_impact = null;
		$riskFormData.probability = 50;
		$riskFormData.potential_impact = null;
		$riskFormData.mitigation_plan = null;
		$riskFormData.date_for_review = undefined;
		$riskFormData.risk_owner_user_id = null;
		$riskFormData.risk_owner_name = null;
		$riskFormData.risk_owner_email = null;
		showRiskDialog = true;
	}

	// Function to open the risk form dialog for editing an existing risk
	function editRisk(risk: (typeof data.risks)[0]) {
		editingRisk = true;
		$riskFormData.project_id = project.project_id;
		$riskFormData.risk_id = risk.risk_id;
		$riskFormData.title = risk.title;
		$riskFormData.description = risk.description;
		$riskFormData.status = risk.status as (typeof riskStatuses)[number];
		$riskFormData.wbs_library_item_id = risk.wbs_library_item_id;
		$riskFormData.date_identified = risk.date_identified || today(getLocalTimeZone()).toString();
		$riskFormData.cause = risk.cause;
		$riskFormData.effect = risk.effect;
		$riskFormData.program_impact = risk.program_impact;
		$riskFormData.probability = risk.probability;
		$riskFormData.potential_impact = risk.potential_impact;
		$riskFormData.mitigation_plan = risk.mitigation_plan;
		$riskFormData.date_for_review = risk.date_for_review || undefined;
		$riskFormData.risk_owner_user_id = risk.risk_owner_user_id;
		$riskFormData.risk_owner_name = risk.risk_owner_name;
		$riskFormData.risk_owner_email = risk.risk_owner_email;
		showRiskDialog = true;
	}

	// Function to confirm risk deletion
	function confirmDeleteRisk(risk: (typeof data.risks)[0]) {
		riskToDelete = risk;
		showDeleteDialog = true;
	}

	// Function to get status badge color
	function getStatusColor(status: string) {
		switch (status) {
			case 'identified':
				return 'bg-yellow-500 text-black';
			case 'assessed':
				return 'bg-blue-500 text-white';
			case 'mitigated':
				return 'bg-green-500';
			case 'occurred':
				return 'bg-red-500 text-white';
			case 'closed':
				return 'bg-gray-500 text-white';
			default:
				return 'bg-gray-500 text-white';
		}
	}

	// Calculate total potential financial impact
	const totalPotentialImpact = $derived(
		risks.reduce((sum, risk) => sum + (risk.potential_impact || 0), 0),
	);

	// Format date for display
	function formatDate(dateString: string | null) {
		if (!dateString) return '';
		return format(new Date(dateString), 'yyyy-MM-dd');
	}

	// Get risk owner display name
	function getRiskOwnerDisplay(risk: (typeof data.risks)[0]) {
		if (risk.risk_owner?.full_name) {
			return risk.risk_owner.full_name;
		} else if (risk.risk_owner?.email) {
			return risk.risk_owner?.email;
		} else if (risk.risk_owner_name) {
			return risk.risk_owner_name;
		} else if (risk.risk_owner_email) {
			return risk.risk_owner_email;
		}
		return 'Not assigned';
	}

	// Date formatter for displaying formatted dates
	const df = new DateFormatter('en-GB', {
		dateStyle: 'long',
	});

	// State variables for date pickers
	const startDate = $derived($filterData.date_from ? parseDate($filterData.date_from) : undefined);
	const endDate = $derived($filterData.date_to ? parseDate($filterData.date_to) : undefined);
	const identifiedDate = $derived(
		$riskFormData.date_identified ? parseDate($riskFormData.date_identified) : undefined,
	);
	const reviewDate = $derived(
		$riskFormData.date_for_review ? parseDate($riskFormData.date_for_review) : undefined,
	);

	// Set placeholder for calendar
	let placeholder = $state<DateValue>(today(getLocalTimeZone()));
</script>

<svelte:head>
	<title>Risk Register - {project.name} - {client.name}</title>
</svelte:head>

<div class="mx-auto mb-12 px-2 py-6 sm:px-6">
	<div class="mb-6 flex items-center justify-between">
		<div>
			<h1 class="text-3xl font-bold tracking-tight">Risk Register</h1>
			<p class="text-muted-foreground mt-2">
				Low-likelihood risks (&lt;= 50% probability) requiring attention
			</p>
		</div>

		{#if canEditProject}
			<Button onclick={addNewRisk}>
				<PlusIcon class="mr-2 h-4 w-4" />
				Add Risk
			</Button>
		{/if}
	</div>

	<!-- Filter Card -->
	<Card class="mb-6">
		<CardHeader>
			<CardTitle>Filters</CardTitle>
			<CardDescription>Filter risks by status, date, or WBS item</CardDescription>
		</CardHeader>
		<CardContent>
			<form
				action="?/applyFilters"
				method="POST"
				use:enhanceFilterForm
				class="grid grid-cols-1 gap-4 md:grid-cols-5"
				class="grid grid-cols-1 gap-4 md:grid-cols-5"
			>
				<div>
					<Form.Field form={filterForm} name="status">
						<Form.Control>
							{#snippet children({ props })}
								<Form.Label>Status</Form.Label>
								<Select.Root type="single" bind:value={$filterData.status} name={props.name}>
									<Select.Trigger {...props}>
										{$filterData.status
											? $filterData.status === 'all'
												? 'All Statuses'
												: $filterData.status.charAt(0).toUpperCase() + $filterData.status.slice(1)
											: 'Select status'}
									</Select.Trigger>
									<Select.Content>
										<Select.Item value="all" label="All Statuses" />
										{#each riskStatuses as status (status)}
											<Select.Item
												value={status}
												label={status.charAt(0).toUpperCase() + status.slice(1)}
											/>
										{/each}
									</Select.Content>
								</Select.Root>
							{/snippet}
						</Form.Control>
						<Form.FieldErrors />
					</Form.Field>
				</div>

				<div>
					<Form.Field form={filterForm} name="date_from">
						<Form.Control>
							{#snippet children({ props })}
								<Form.Label>From Date</Form.Label>
								<Popover.Root>
									<Popover.Trigger
								<Popover.Root>
									<Popover.Trigger
										{...props}
										class={cn(
											buttonVariants({ variant: 'outline' }),
											'w-full justify-start text-left font-normal',
											!startDate && 'text-muted-foreground',
										)}
									>
										{startDate
											? df.format(startDate.toDate(getLocalTimeZone()))
											: 'Pick a start date'}
										<CalendarIcon class="ml-auto h-4 w-4 opacity-50" />
									</Popover.Trigger>
									<Popover.Content class="w-auto p-0" side="top">
									</Popover.Trigger>
									<Popover.Content class="w-auto p-0" side="top">
										<Calendar
											type="single"
											value={startDate as DateValue}
											bind:placeholder
											calendarLabel="Start date"
											onValueChange={(v) => {
												if (v) {
													$filterData.date_from = v.toString();
												} else {
													$filterData.date_from = undefined;
												}
											}}
										/>
									</Popover.Content>
								</Popover.Root>
									</Popover.Content>
								</Popover.Root>
								<Form.FieldErrors />
								<input hidden value={$filterData.date_from} name={props.name} />
							{/snippet}
						</Form.Control>
					</Form.Field>
				</div>

				<div>
					<Form.Field form={filterForm} name="date_to">
						<Form.Control>
							{#snippet children({ props })}
								<Form.Label>To Date</Form.Label>
								<Popover.Root>
									<Popover.Trigger
								<Popover.Root>
									<Popover.Trigger
										{...props}
										class={cn(
											buttonVariants({ variant: 'outline' }),
											'w-full justify-start text-left font-normal',
											!endDate && 'text-muted-foreground',
										)}
									>
										{endDate ? df.format(endDate.toDate(getLocalTimeZone())) : 'Pick an end date'}
										<CalendarIcon class="ml-auto h-4 w-4 opacity-50" />
									</Popover.Trigger>
									<Popover.Content class="w-auto p-0" side="top">
									</Popover.Trigger>
									<Popover.Content class="w-auto p-0" side="top">
										<Calendar
											type="single"
											value={endDate as DateValue}
											bind:placeholder
											calendarLabel="End date"
											onValueChange={(v) => {
												if (v) {
													$filterData.date_to = v.toString();
												} else {
													$filterData.date_to = undefined;
												}
											}}
										/>
									</Popover.Content>
								</Popover.Root>
									</Popover.Content>
								</Popover.Root>
								<Form.FieldErrors />
								<input hidden value={$filterData.date_to} name={props.name} />
							{/snippet}
						</Form.Control>
					</Form.Field>
				</div>

				<div class="col-span-2">
					<Form.Field form={filterForm} name="wbs_library_item_id" class="flex flex-col">
						<Popover.Root bind:open={filterOpen}>
							<Form.Control id={filterTriggerId}>
								{#snippet children({ props })}
									<Form.Label>WBS Item</Form.Label>
									<Popover.Trigger
										class={cn(
											buttonVariants({ variant: 'outline' }),
											'w-full justify-between truncate',
											!$filterData.wbs_library_item_id && 'text-muted-foreground',
										)}
										role="combobox"
										bind:ref={filterTriggerRef}
										{...props}
									>
				<div class="col-span-2">
					<Form.Field form={filterForm} name="wbs_library_item_id" class="flex flex-col">
						<Popover.Root bind:open={filterOpen}>
							<Form.Control id={filterTriggerId}>
								{#snippet children({ props })}
									<Form.Label>WBS Item</Form.Label>
									<Popover.Trigger
										class={cn(
											buttonVariants({ variant: 'outline' }),
											'w-full justify-between truncate',
											!$filterData.wbs_library_item_id && 'text-muted-foreground',
										)}
										role="combobox"
										bind:ref={filterTriggerRef}
										{...props}
									>
										{$filterData.wbs_library_item_id
											? getWbsItemName($filterData.wbs_library_item_id)
											? getWbsItemName($filterData.wbs_library_item_id)
											: 'Select WBS item'}
										<CaretUpDownIcon class="ml-2 size-4 shrink-0 opacity-50" />
									</Popover.Trigger>
									<input hidden value={$filterData.wbs_library_item_id} name={props.name} />
								{/snippet}
							</Form.Control>
							<Popover.Content
								class="w-(--bits-popover-anchor-width) p-0"
								side="bottom"
								align="start"
							>
								<Command.Root>
									<Command.Input autofocus placeholder="Search WBS items..." class="h-9" />
									<Command.Empty>No matching items found.</Command.Empty>
									<Command.Group class="max-h-[300px] overflow-y-auto">
										<Command.Item
											value="All WBS Items"
											onSelect={() => {
												$filterData.wbs_library_item_id = undefined;
												closeAndFocusTrigger(filterTriggerId);
											}}
										>
											All WBS Items
											<CheckIcon
												class={cn(
													'ml-auto size-4',
													$filterData.wbs_library_item_id && 'text-transparent',
												)}
											/>
										</Command.Item>
										{#each wbsItems as option (option.value)}
											<Command.Item
												value={option.label}
												onSelect={() => {
													$filterData.wbs_library_item_id = option.value;
													closeAndFocusTrigger(filterTriggerId);
												}}
											>
												{option.label}
												<CheckIcon
													class={cn(
														'ml-auto size-4',
														option.value !== $filterData.wbs_library_item_id && 'text-transparent',
													)}
												/>
											</Command.Item>
										{/each}
									</Command.Group>
								</Command.Root>
							</Popover.Content>
						</Popover.Root>
						<Form.FieldErrors />
										<CaretUpDownIcon class="ml-2 size-4 shrink-0 opacity-50" />
									</Popover.Trigger>
									<input hidden value={$filterData.wbs_library_item_id} name={props.name} />
								{/snippet}
							</Form.Control>
							<Popover.Content
								class="w-(--bits-popover-anchor-width) p-0"
								side="bottom"
								align="start"
							>
								<Command.Root>
									<Command.Input autofocus placeholder="Search WBS items..." class="h-9" />
									<Command.Empty>No matching items found.</Command.Empty>
									<Command.Group class="max-h-[300px] overflow-y-auto">
										<Command.Item
											value="All WBS Items"
											onSelect={() => {
												$filterData.wbs_library_item_id = undefined;
												closeAndFocusTrigger(filterTriggerId);
											}}
										>
											All WBS Items
											<CheckIcon
												class={cn(
													'ml-auto size-4',
													$filterData.wbs_library_item_id && 'text-transparent',
												)}
											/>
										</Command.Item>
										{#each wbsItems as option (option.value)}
											<Command.Item
												value={option.label}
												onSelect={() => {
													$filterData.wbs_library_item_id = option.value;
													closeAndFocusTrigger(filterTriggerId);
												}}
											>
												{option.label}
												<CheckIcon
													class={cn(
														'ml-auto size-4',
														option.value !== $filterData.wbs_library_item_id && 'text-transparent',
													)}
												/>
											</Command.Item>
										{/each}
									</Command.Group>
								</Command.Root>
							</Popover.Content>
						</Popover.Root>
						<Form.FieldErrors />
					</Form.Field>
				</div>

				<div class="flex justify-end md:col-span-5">
					<Button type="submit" disabled={$submittingFilterForm}>
						{#if $delayedFilterForm}
							<SpinnerGapIcon class="text-primary size-4 animate-spin" />
						{/if}
						Apply Filters</Button
					>
				</div>
			</form>
		</CardContent>
	</Card>

	<!-- Summary Card -->
	<Card class="mb-6">
		<CardHeader>
			<CardTitle>Risk Register Summary</CardTitle>
		</CardHeader>
		<CardContent>
			<div class="grid grid-cols-1 gap-4 md:grid-cols-3">
				<div>
					<h3 class="text-lg font-medium">Total Risks</h3>
					<p class="text-3xl font-bold">{risks.length}</p>
				</div>
				<div>
					<h3 class="text-lg font-medium">Total Potential Impact</h3>
					<p class="text-3xl font-bold">{formatCurrency(totalPotentialImpact)}</p>
				</div>
				<div>
					<h3 class="text-lg font-medium">Status Breakdown</h3>
					<div class="mt-2 flex flex-wrap gap-2">
						{#each riskStatuses as status (status)}
							{@const count = risks.filter((r) => r.status === status).length}
							<Badge
								variant="outline"
								class={cn('capitalize', count > 0 ? getStatusColor(status) : '')}
							>
								{status}: {count}
							</Badge>
						{/each}
					</div>
				</div>
			</div>
		</CardContent>
	</Card>

	<!-- Risks Table -->
	<div class="rounded-md border">
		<Table class="table-fixed">
			<TableHeader>
				<TableRow>
					<TableHead class="w-60">Title</TableHead>
					<TableHead class="w-24">Status</TableHead>
					<TableHead class="w-48">WBS Item</TableHead>
					<TableHead class="w-24 text-right whitespace-normal">Date Identified</TableHead>
					<TableHead class="w-18 text-right">Probability</TableHead>
					<TableHead class="w-24 text-right  whitespace-normal">Potential Impact</TableHead>
					<TableHead class="w-40">Risk Owner</TableHead>
					<TableHead class="w-16 pr-6 text-right">Actions</TableHead>
				</TableRow>
			</TableHeader>
			<TableBody>
				{#if risks.length === 0}
					<TableRow>
						<TableCell class="py-4 text-center" colspan={8}>No risks found</TableCell>
					</TableRow>
				{:else}
					{#each risks as risk (risk.risk_id)}
						<TableRow>
							<TableCell class="font-medium">
								<div class="max-w-full">
									<p class="text-wrap">
										{risk.title}
									</p>
									<p class="text-muted-foreground max-w-60 text-xs text-wrap">{risk.description}</p>
								</div>
							</TableCell>
							<TableCell>
								<Badge class={cn('capitalize', getStatusColor(risk.status))}>
									{risk.status}
								</Badge>
							</TableCell>
							<TableCell
								><p class="text-wrap">
									{risk.wbs_item ? `${risk.wbs_item.code} - ${risk.wbs_item.description}` : '-'}
								</p>
							</TableCell>
							<TableCell class="text-right">{formatDate(risk.date_identified)}</TableCell>
							<TableCell class="text-right">{risk.probability}%</TableCell>
							<TableCell class="text-right"
								>{risk.potential_impact ? formatCurrency(risk.potential_impact) : '-'}</TableCell
							>
							<TableCell>{getRiskOwnerDisplay(risk)}</TableCell>
							<TableCell class="text-right">
								{#if canEditProject}
									<DropdownMenu.Root>
										<DropdownMenu.Trigger class="px-2">
											<DotsThreeVerticalIcon />
										</DropdownMenu.Trigger>
										<DropdownMenu.Content align="end">
											<DropdownMenu.Item onclick={() => editRisk(risk)}>
												<PencilIcon class="mr-2 h-4 w-4" />
												Edit
											</DropdownMenu.Item>
											<DropdownMenu.Item onclick={() => confirmDeleteRisk(risk)}>
												<TrashIcon class="mr-2 h-4 w-4" />
												Delete
											</DropdownMenu.Item>
										</DropdownMenu.Content>
									</DropdownMenu.Root>
								{/if}
							</TableCell>
						</TableRow>
					{/each}
				{/if}
			</TableBody>
		</Table>
	</div>
</div>

<!-- Risk Form Dialog -->
<Dialog bind:open={showRiskDialog}>
	<DialogContent class="max-h-11/12 max-w-3xl overflow-auto">
		<DialogHeader>
			<DialogTitle>{editingRisk ? 'Edit Risk' : 'Add New Risk'}</DialogTitle>
			<DialogDescription>
				{editingRisk ? 'Update the risk details below' : 'Enter the details for the new risk'}
			</DialogDescription>
		</DialogHeader>

		<form action="?/upsertRisk" method="POST" use:enhanceRiskForm class="space-y-4">
			<input type="hidden" name="project_id" value={project.project_id} />
			{#if editingRisk && $riskFormData.risk_id}
				<input type="hidden" name="risk_id" value={$riskFormData.risk_id} />
			{/if}

			<div class="grid grid-cols-1 gap-4 md:grid-cols-2">
				<!-- Title -->
				<div class="md:col-span-2">
					<Form.Field form={riskForm} name="title">
						<Form.Control>
							{#snippet children({ props })}
								<Form.Label>Title</Form.Label>
								<Input {...props} bind:value={$riskFormData.title} />
							{/snippet}
						</Form.Control>
						<Form.FieldErrors />
					</Form.Field>
				</div>

				<!-- Description -->
				<div class="md:col-span-2">
					<Form.Field form={riskForm} name="description">
						<Form.Control>
							{#snippet children({ props })}
								<Form.Label>Description</Form.Label>
								<Textarea {...props} rows={3} bind:value={$riskFormData.description} />
							{/snippet}
						</Form.Control>
						<Form.FieldErrors />
					</Form.Field>
				</div>

				<!-- Status -->
				<div>
					<Form.Field form={riskForm} name="status">
						<Form.Control>
							{#snippet children({ props })}
								<Form.Label>Status</Form.Label>
								<Select.Root type="single" bind:value={$riskFormData.status} name={props.name}>
									<Select.Trigger {...props}>
										{$riskFormData.status
											? $riskFormData.status.charAt(0).toUpperCase() + $riskFormData.status.slice(1)
											: 'Select status'}
									</Select.Trigger>
									<Select.Content>
										{#each riskStatuses as status (status)}
											<Select.Item
												value={status}
												label={status.charAt(0).toUpperCase() + status.slice(1)}
											/>
										{/each}
									</Select.Content>
								</Select.Root>
							{/snippet}
						</Form.Control>
						<Form.FieldErrors />
					</Form.Field>
				</div>

				<!-- WBS Item -->
				<div>
					<Form.Field form={riskForm} name="wbs_library_item_id" class="flex flex-col">
						<Popover.Root bind:open={dialogOpen}>
							<Form.Control id={dialogTriggerId}>
								{#snippet children({ props })}
									<Form.Label>WBS Item</Form.Label>
									<Popover.Trigger
										class={cn(
											buttonVariants({ variant: 'outline' }),
											'w-full justify-between truncate',
											!$riskFormData.wbs_library_item_id && 'text-muted-foreground',
										)}
										role="combobox"
										bind:ref={dialogTriggerRef}
										{...props}
									>
										{$riskFormData.wbs_library_item_id
											? getWbsItemName($riskFormData.wbs_library_item_id)
											? getWbsItemName($riskFormData.wbs_library_item_id)
											: 'Select WBS item'}
										<CaretUpDownIcon class="ml-2 size-4 shrink-0 opacity-50" />
									</Popover.Trigger>
									<input hidden value={$riskFormData.wbs_library_item_id} name={props.name} />
								{/snippet}
							</Form.Control>
							<Popover.Content
								class="w-(--bits-popover-anchor-width) p-0"
								side="bottom"
								align="start"
							>
								<Command.Root>
									<Command.Input autofocus placeholder="Search WBS items..." class="h-9" />
									<Command.Empty>No matching items found.</Command.Empty>
									<Command.Group class="max-h-[300px] overflow-y-auto">
										<Command.Item
											value="None"
											onSelect={() => {
												$riskFormData.wbs_library_item_id = null;
												closeAndFocusTrigger(dialogTriggerId, true);
											}}
										>
											None
											<CheckIcon
												class={cn(
													'ml-auto size-4',
													$riskFormData.wbs_library_item_id && 'text-transparent',
												)}
											/>
										</Command.Item>
										{#each wbsItems as option (option.value)}
											<Command.Item
												value={option.label}
												onSelect={() => {
													$riskFormData.wbs_library_item_id = option.value;
													closeAndFocusTrigger(dialogTriggerId, true);
												}}
											>
												{option.label}
												<CheckIcon
													class={cn(
														'ml-auto size-4',
														option.value !== $riskFormData.wbs_library_item_id &&
															'text-transparent',
													)}
												/>
											</Command.Item>
										{/each}
									</Command.Group>
								</Command.Root>
							</Popover.Content>
						</Popover.Root>
										<CaretUpDownIcon class="ml-2 size-4 shrink-0 opacity-50" />
									</Popover.Trigger>
									<input hidden value={$riskFormData.wbs_library_item_id} name={props.name} />
								{/snippet}
							</Form.Control>
							<Popover.Content
								class="w-(--bits-popover-anchor-width) p-0"
								side="bottom"
								align="start"
							>
								<Command.Root>
									<Command.Input autofocus placeholder="Search WBS items..." class="h-9" />
									<Command.Empty>No matching items found.</Command.Empty>
									<Command.Group class="max-h-[300px] overflow-y-auto">
										<Command.Item
											value="None"
											onSelect={() => {
												$riskFormData.wbs_library_item_id = null;
												closeAndFocusTrigger(dialogTriggerId, true);
											}}
										>
											None
											<CheckIcon
												class={cn(
													'ml-auto size-4',
													$riskFormData.wbs_library_item_id && 'text-transparent',
												)}
											/>
										</Command.Item>
										{#each wbsItems as option (option.value)}
											<Command.Item
												value={option.label}
												onSelect={() => {
													$riskFormData.wbs_library_item_id = option.value;
													closeAndFocusTrigger(dialogTriggerId, true);
												}}
											>
												{option.label}
												<CheckIcon
													class={cn(
														'ml-auto size-4',
														option.value !== $riskFormData.wbs_library_item_id &&
															'text-transparent',
													)}
												/>
											</Command.Item>
										{/each}
									</Command.Group>
								</Command.Root>
							</Popover.Content>
						</Popover.Root>
						<Form.FieldErrors />
					</Form.Field>
				</div>

				<!-- Date Identified -->
				<div>
					<Form.Field form={riskForm} name="date_identified">
						<Form.Control>
							{#snippet children({ props })}
								<Form.Label>Date Identified</Form.Label>
								<Popover.Root>
									<Popover.Trigger
								<Popover.Root>
									<Popover.Trigger
										{...props}
										class={cn(
											buttonVariants({ variant: 'outline' }),
											'w-full justify-start text-left font-normal',
											!identifiedDate && 'text-muted-foreground',
										)}
									>
										{identifiedDate
											? df.format(identifiedDate.toDate(getLocalTimeZone()))
											: 'Pick a date'}
										<CalendarIcon class="ml-auto h-4 w-4 opacity-50" />
									</Popover.Trigger>
									<Popover.Content class="w-auto p-0" side="top">
									</Popover.Trigger>
									<Popover.Content class="w-auto p-0" side="top">
										<Calendar
											type="single"
											value={identifiedDate as DateValue}
											bind:placeholder
											calendarLabel="Date identified"
											onValueChange={(v) => {
												if (v) {
													$riskFormData.date_identified = v.toString();
												} else {
													$riskFormData.date_identified = undefined;
												}
											}}
										/>
									</Popover.Content>
								</Popover.Root>
									</Popover.Content>
								</Popover.Root>
								<Form.FieldErrors />
								<input hidden value={$riskFormData.date_identified} name={props.name} />
							{/snippet}
						</Form.Control>
					</Form.Field>
				</div>

				<!-- Date for Review -->
				<div>
					<Form.Field form={riskForm} name="date_for_review">
						<Form.Control>
							{#snippet children({ props })}
								<Form.Label>Date for Review</Form.Label>
								<Popover.Root>
									<Popover.Trigger
								<Popover.Root>
									<Popover.Trigger
										{...props}
										class={cn(
											buttonVariants({ variant: 'outline' }),
											'w-full justify-start text-left font-normal',
											!reviewDate && 'text-muted-foreground',
										)}
									>
										{reviewDate ? df.format(reviewDate.toDate(getLocalTimeZone())) : 'Pick a date'}
										<CalendarIcon class="ml-auto h-4 w-4 opacity-50" />
									</Popover.Trigger>
									<Popover.Content class="w-auto p-0" side="top">
									</Popover.Trigger>
									<Popover.Content class="w-auto p-0" side="top">
										<Calendar
											type="single"
											value={reviewDate as DateValue}
											bind:placeholder
											calendarLabel="Review date"
											onValueChange={(v) => {
												if (v) {
													$riskFormData.date_for_review = v.toString();
												} else {
													$riskFormData.date_for_review = undefined;
												}
											}}
										/>
									</Popover.Content>
								</Popover.Root>
									</Popover.Content>
								</Popover.Root>
								<Form.FieldErrors />
								<input hidden value={$riskFormData.date_for_review} name={props.name} />
							{/snippet}
						</Form.Control>
					</Form.Field>
				</div>

				<!-- Cause -->
				<div class="md:col-span-2">
					<Form.Field form={riskForm} name="cause">
						<Form.Control>
							{#snippet children({ props })}
								<Form.Label>Cause</Form.Label>
								<Textarea {...props} rows={2} bind:value={$riskFormData.cause} />
							{/snippet}
						</Form.Control>
						<Form.FieldErrors />
					</Form.Field>
				</div>

				<!-- Effect -->
				<div class="md:col-span-2">
					<Form.Field form={riskForm} name="effect">
						<Form.Control>
							{#snippet children({ props })}
								<Form.Label>Effect</Form.Label>
								<Textarea {...props} rows={2} bind:value={$riskFormData.effect} />
							{/snippet}
						</Form.Control>
						<Form.FieldErrors />
					</Form.Field>
				</div>

				<!-- Program Impact -->
				<div class="md:col-span-2">
					<Form.Field form={riskForm} name="program_impact">
						<Form.Control>
							{#snippet children({ props })}
								<Form.Label>Program Impact</Form.Label>
								<Textarea {...props} rows={2} bind:value={$riskFormData.program_impact} />
							{/snippet}
						</Form.Control>
						<Form.FieldErrors />
					</Form.Field>
				</div>

				<!-- Probability -->
				<div>
					<Form.Field form={riskForm} name="probability">
						<Form.Control>
							{#snippet children({ props })}
								<Form.Label>Probability (%)</Form.Label>
								<Input
									{...props}
									type="number"
									min="0"
									max="100"
									bind:value={$riskFormData.probability}
								/>
							{/snippet}
						</Form.Control>
						<Form.FieldErrors />
					</Form.Field>
				</div>

				<!-- Potential Impact -->
				<div>
					<Form.Field form={riskForm} name="potential_impact">
						<Form.Control>
							{#snippet children({ props })}
								<Form.Label>Potential Financial Impact</Form.Label>
								<Input
									{...props}
									type="number"
									step="0.01"
									bind:value={$riskFormData.potential_impact}
								/>
							{/snippet}
						</Form.Control>
						<Form.FieldErrors />
					</Form.Field>
				</div>

				<!-- Mitigation Plan -->
				<div class="md:col-span-2">
					<Form.Field form={riskForm} name="mitigation_plan">
						<Form.Control>
							{#snippet children({ props })}
								<Form.Label>Mitigation Plan</Form.Label>
								<Textarea {...props} rows={3} bind:value={$riskFormData.mitigation_plan} />
							{/snippet}
						</Form.Control>
						<Form.FieldErrors />
					</Form.Field>
				</div>

				<!-- Risk Owner -->
				<div class="md:col-span-2">
					<Form.Field form={riskForm} name="risk_owner_user_id">
						<Form.Control>
							{#snippet children({ props })}
								<Form.Label>Risk Owner</Form.Label>
								<Select.Root type="single" name={props.name}>
									<Select.Trigger {...props}>
										{$riskFormData.risk_owner_user_id
											? projectMembers.find(
													(member) => member.user_id === $riskFormData.risk_owner_user_id,
												)
												? `${projectMembers.find((member) => member.user_id === $riskFormData.risk_owner_user_id)?.full_name} (${projectMembers.find((member) => member.user_id === $riskFormData.risk_owner_user_id)?.email})`
												: 'Select risk owner'
											: 'Select risk owner'}
									</Select.Trigger>
									<Select.Content>
										<Select.Item value="none" label="None" />
										{#each projectMembers as member (member.user_id)}
											<Select.Item
												value={member.user_id}
												label={`${member.full_name} (${member.email})`}
											/>
										{/each}
									</Select.Content>
								</Select.Root>
							{/snippet}
						</Form.Control>
						<Form.FieldErrors />
					</Form.Field>
				</div>

				<!-- External Risk Owner Name -->
				<div>
					<Form.Field form={riskForm} name="risk_owner_name">
						<Form.Control>
							{#snippet children({ props })}
								<Form.Label>External Risk Owner Name</Form.Label>
								<Input {...props} bind:value={$riskFormData.risk_owner_name} />
							{/snippet}
						</Form.Control>
						<Form.FieldErrors />
					</Form.Field>
				</div>

				<!-- External Risk Owner Email -->
				<div>
					<Form.Field form={riskForm} name="risk_owner_email">
						<Form.Control>
							{#snippet children({ props })}
								<Form.Label>External Risk Owner Email</Form.Label>
								<Input {...props} type="email" bind:value={$riskFormData.risk_owner_email} />
							{/snippet}
						</Form.Control>
						<Form.FieldErrors />
					</Form.Field>
				</div>
			</div>

			<DialogFooter>
				<Button type="button" variant="outline" onclick={() => (showRiskDialog = false)}
					>Cancel</Button
				>
				<Button type="submit" disabled={$submittingRiskForm}>
					{#if $delayedRiskForm}
						<SpinnerGapIcon class="text-primary size-4 animate-spin" />
					{/if}
					{editingRisk ? 'Update Risk' : 'Add Risk'}
				</Button>
			</DialogFooter>
		</form>
	</DialogContent>
</Dialog>

<!-- Delete Confirmation Dialog -->
<Dialog bind:open={showDeleteDialog}>
	<DialogContent>
		<DialogHeader>
			<DialogTitle>Confirm Deletion</DialogTitle>
			<DialogDescription>
				Are you sure you want to delete this risk? This action cannot be undone.
			</DialogDescription>
		</DialogHeader>

		<form action="?/deleteRisk" method="POST">
			<input type="hidden" name="risk_id" value={riskToDelete?.risk_id} />
			<input type="hidden" name="project_id" value={project.project_id} />

			<DialogFooter>
				<Button type="button" variant="outline" onclick={() => (showDeleteDialog = false)}
					>Cancel</Button
				>
				<Button type="submit" variant="destructive">Delete Risk</Button>
			</DialogFooter>
		</form>
	</DialogContent>
</Dialog>
